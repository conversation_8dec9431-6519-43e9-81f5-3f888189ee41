import React, { useEffect, useRef, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import SimplePDFViewer from './SimplePDFViewer';

interface VimeoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoId?: string;
  contentType?: 'video' | 'pdf';
  pdfUrl?: string;
}

interface VideoSize {
  width: number;
  height: number;
}

const VimeoModal: React.FC<VimeoModalProps> = ({ isOpen, onClose, videoId, contentType = 'video', pdfUrl }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [videoSize, setVideoSize] = useState<VideoSize>({ width: 1200, height: 675 }); // Default 16:9 aspect ratio

  // Function to calculate optimal video size based on viewport and aspect ratio
  const calculateVideoSize = useCallback((aspectRatio: number = 16/9): VideoSize => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const isMobile = viewportWidth <= 768;
    const isSmallMobile = viewportWidth <= 480;

    // Account for modal padding and margins based on device type
    let maxWidthPercent = 0.9;
    let maxHeightPercent = 0.8;
    let maxWidthCap = 1400;
    let maxHeightCap = 800;

    if (isSmallMobile) {
      maxWidthPercent = 0.98;
      maxHeightPercent = 0.85;
      maxWidthCap = viewportWidth - 10;
      maxHeightCap = viewportHeight - 80;
    } else if (isMobile) {
      maxWidthPercent = 0.95;
      maxHeightPercent = 0.82;
      maxWidthCap = viewportWidth - 20;
      maxHeightCap = viewportHeight - 100;
    }

    const maxWidth = Math.min(viewportWidth * maxWidthPercent, maxWidthCap);
    const maxHeight = Math.min(viewportHeight * maxHeightPercent, maxHeightCap);

    // Calculate dimensions based on aspect ratio
    let width = maxWidth;
    let height = width / aspectRatio;

    // If height exceeds max height, adjust based on height
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    // Ensure minimum size for usability
    const minWidth = Math.min(isMobile ? 300 : 400, viewportWidth * 0.8);
    const minHeight = minWidth / aspectRatio;

    return {
      width: Math.max(width, minWidth),
      height: Math.max(height, minHeight)
    };
  }, []);

  // Update video size when modal opens or viewport changes
  useEffect(() => {
    if (isOpen && contentType === 'video') {
      const newSize = calculateVideoSize();
      setVideoSize(newSize);

      const handleResize = () => {
        const newSize = calculateVideoSize();
        setVideoSize(newSize);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isOpen, contentType, calculateVideoSize]);

  // Close modal on escape key and prevent body scroll when modal is open
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      // Prevent vertical scroll but keep horizontal hidden when modal is open
      document.body.style.overflowY = 'hidden';
      document.body.style.overflowX = 'hidden';
      document.documentElement.style.overflowY = 'hidden';
      document.documentElement.style.overflowX = 'hidden';
      document.addEventListener('keydown', handleEscape);
    } else {
      // Restore vertical scroll but keep horizontal hidden when modal is closed
      document.body.style.overflowY = 'auto';
      document.body.style.overflowX = 'hidden';
      document.documentElement.style.overflowY = 'auto';
      document.documentElement.style.overflowX = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Always ensure horizontal scroll is hidden on cleanup
      document.body.style.overflowY = 'auto';
      document.body.style.overflowX = 'hidden';
      document.documentElement.style.overflowY = 'auto';
      document.documentElement.style.overflowX = 'hidden';
    };
  }, [isOpen, onClose]);

  // Autoplay effect for Vimeo video
  useEffect(() => {
    if (isOpen && contentType === 'video' && iframeRef.current) {
      // Small delay to ensure iframe is loaded
      const timer = setTimeout(() => {
        if (iframeRef.current) {
          // Try to trigger autoplay via postMessage to Vimeo player
          try {
            iframeRef.current.contentWindow?.postMessage('{"method":"play"}', '*');
          } catch (error) {
            console.log('Autoplay attempt failed:', error);
          }
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isOpen, contentType]);

  if (!isOpen) return null;

  // Get current scroll position to center modal relative to user's view
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const viewportHeight = window.innerHeight;

  return createPortal(
    <div
      className={`modal-overlay ${isOpen ? 'modal-overlay-open' : 'modal-overlay-close'}`}
      style={{
        top: scrollTop,
        height: viewportHeight,
        minHeight: viewportHeight
      }}
    >
      {/* Black screen overlay */}
      <div
        className={`modal-backdrop ${isOpen ? 'modal-backdrop-open' : 'modal-backdrop-close'}`}
        onClick={onClose}
      />

      {/* Modal Content Container */}
      <div className="modal-container">
        <div
          className={`modal-content ${isOpen ? 'modal-content-open' : 'modal-content-close'}`}
          onClick={(e) => e.stopPropagation()}
          style={contentType === 'video' ? {
            width: `${videoSize.width}px`,
            height: `${videoSize.height + 40}px`, // Add padding for close button
            maxWidth: `${videoSize.width}px`,
            maxHeight: `${videoSize.height + 40}px`,
            minHeight: `${videoSize.height + 40}px`
          } : undefined}
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            className="modal-close-btn"
            aria-label="Close modal"
          >
            <X size={28} />
          </button>

          {/* Dynamic Content */}
          <div className="modal-body" style={contentType === 'video' ? { padding: 0 } : undefined}>
            {contentType === 'video' && videoId ? (
              <div
                className="modal-video-container"
                style={{
                  width: `${videoSize.width}px`,
                  height: `${videoSize.height}px`,
                  position: 'relative'
                }}
              >
                <iframe
                  ref={iframeRef}
                  src={`https://player.vimeo.com/video/1103582311?badge=0&amp;autopause=0&amp;autoplay=1&amp;muted=1&amp;player_id=0&amp;app_id=58479`}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    border: 0
                  }}
                  allow="autoplay; fullscreen; picture-in-picture; clipboard-write; encrypted-media; web-share"
                  referrerPolicy="strict-origin-when-cross-origin"
                  title="Trunk Growth Animation"
                  className="modal-iframe"
                />
              </div>
            ) : contentType === 'pdf' && pdfUrl ? (
              <div className="modal-pdf-viewer">
                <SimplePDFViewer pdfUrl={pdfUrl} />
              </div>
            ) : (
              <div className="modal-fallback">
                <h2 className="modal-title">Content Loading...</h2>
                <p className="modal-subtitle">Please wait</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default VimeoModal;
